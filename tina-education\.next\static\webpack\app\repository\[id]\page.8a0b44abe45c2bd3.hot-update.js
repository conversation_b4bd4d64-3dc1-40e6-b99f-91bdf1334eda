"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/repository/[id]/page",{

/***/ "(app-pages-browser)/./app/repository/[id]/page.tsx":
/*!**************************************!*\
  !*** ./app/repository/[id]/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicationDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineHeart!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaHeart!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_quantitycounter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/quantitycounter */ \"(app-pages-browser)/./app/components/quantitycounter.tsx\");\n/* harmony import */ var _components_AuthRequiredButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/AuthRequiredButton */ \"(app-pages-browser)/./app/components/AuthRequiredButton.tsx\");\n/* harmony import */ var _components_AuthRequiredLink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/AuthRequiredLink */ \"(app-pages-browser)/./app/components/AuthRequiredLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Sample related books data\nconst relatedBooks = [\n    {\n        id: \"1\",\n        title: \"The Silent Patient\",\n        author: \"Alex Michaelides\",\n        price: \"3000frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"2\",\n        title: \"Gone Girl\",\n        author: \"Gillian Flynn\",\n        price: \"2500frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"3\",\n        title: \"The Girl with the Dragon Tattoo\",\n        author: \"Stieg Larsson\",\n        price: \"3500frs\",\n        type: \"eBook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"4\",\n        title: \"Big Little Lies\",\n        author: \"Liane Moriarty\",\n        price: \"2800frs\",\n        type: \"Audiobook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"5\",\n        title: \"The Woman in the Window\",\n        author: \"A.J. Finn\",\n        price: \"3200frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"6\",\n        title: \"Sharp Objects\",\n        author: \"Gillian Flynn\",\n        price: \"2700frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    }\n];\nfunction PublicationDetailPage(param) {\n    let { params } = param;\n    var _publication_genre, _publication_user, _publication_genre1, _publication_user1;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_3__.useAsync)(params);\n    const [publication, setPublication] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"BOOK\"); // Default to printed copy\n    const [isInWishlist, setIsInWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [wishlistLoading, setWishlistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"PublicationDetailPage.useEffect\": ()=>{\n            async function fetchPublication() {\n                try {\n                    const response = await fetch(\"/api/repository/publication/\".concat(id));\n                    if (response.ok) {\n                        const data = await response.json();\n                        setPublication(data);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch publication:\", error);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchPublication();\n        }\n    }[\"PublicationDetailPage.useEffect\"], [\n        id\n    ]);\n    // Check wishlist status when publication or selectedType changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"PublicationDetailPage.useEffect\": ()=>{\n            async function checkWishlistStatus() {\n                if (!publication) return;\n                try {\n                    const response = await fetch(\"/api/wishlist/check?publicationId=\".concat(id, \"&selectedType=\").concat(selectedType));\n                    if (response.ok) {\n                        const data = await response.json();\n                        setIsInWishlist(data.isInWishlist);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to check wishlist status:\", error);\n                }\n            }\n            checkWishlistStatus();\n        }\n    }[\"PublicationDetailPage.useEffect\"], [\n        id,\n        selectedType,\n        publication\n    ]);\n    const handleWishlistToggle = async ()=>{\n        if (wishlistLoading) return;\n        setWishlistLoading(true);\n        try {\n            if (isInWishlist) {\n                // Remove from wishlist\n                const response = await fetch(\"/api/wishlist?publicationId=\".concat(id, \"&selectedType=\").concat(selectedType), {\n                    method: \"DELETE\"\n                });\n                if (response.ok) {\n                    setIsInWishlist(false);\n                }\n            } else {\n                // Add to wishlist\n                const response = await fetch(\"/api/wishlist\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        publicationId: id,\n                        selectedType\n                    })\n                });\n                if (response.ok) {\n                    setIsInWishlist(true);\n                }\n            }\n        } catch (error) {\n            console.error(\"Failed to update wishlist:\", error);\n        } finally{\n            setWishlistLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    if (!publication) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Publication not found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white mt-18\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-18 flex items-center justify-center\",\n                style: {\n                    backgroundColor: \"#f0eded\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/books\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/authors\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Authors\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/journals\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Journals\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-4 border-b border-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"text-s text-black\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"hover:underline\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        ((_publication_genre = publication.genre) === null || _publication_genre === void 0 ? void 0 : _publication_genre.parent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.parent.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.parent.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        publication.genre && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: publication.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2/5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-sm\",\n                                children: publication.cover ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: publication.cover,\n                                    alt: publication.title,\n                                    width: 270,\n                                    height: 380,\n                                    className: \"w-full h-auto border border-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full aspect-[270/380] bg-gray-200 border border-gray-300 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"No Cover Available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3/5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-black mb-4\",\n                                    children: publication.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-black mb-6\",\n                                    children: [\n                                        \"By \",\n                                        ((_publication_user = publication.user) === null || _publication_user === void 0 ? void 0 : _publication_user.name) || \"Unknown Author\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"border-blue-500 mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mt-20  gap-2.5 mb-35\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"BOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"BOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"Printed Copy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"3000frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"EBOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"EBOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"eBook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"2500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"AUDIOBOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"AUDIOBOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"Audio book\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"3500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mt-20 mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl text-black\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quantitycounter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedType === \"EBOOK\" || selectedType === \"AUDIOBOOK\" || selectedType === \"JOURNAL\" || selectedType === \"ARTICLE\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: \"Available\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : // For physical books, you could add stock logic here\n                                        // For now, showing as available but this could be conditional based on actual stock\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: \"In stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthRequiredLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            href: \"/cart\",\n                                            className: \"px-8 py-4 w-70 h-11 text-white justify-center flex items-center text-xl hover:opacity-90 transition-opacity\",\n                                            style: {\n                                                backgroundColor: \"#0c0a46\"\n                                            },\n                                            children: \"Add to Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthRequiredButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            onClick: handleWishlistToggle,\n                                            disabled: wishlistLoading,\n                                            className: \"px-3 py-2 w-70 h-11 border border-blue-300 flex items-center justify-center gap-6 transition-colors \".concat(wishlistLoading ? \"bg-gray-200 cursor-not-allowed\" : \"bg-gray-300 hover:bg-gray-400\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl \".concat(isInWishlist ? \"text-red-500\" : \"text-gray-700\"),\n                                                    children: isInWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 35\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: isInWishlist ? \"Remove from wishlist\" : \"Add to wish list\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pe-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.abstract || \"A captivating mystery novel that will keep you on the edge of your seat. Follow the intricate plot as secrets unfold and characters navigate through unexpected twists and turns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Book Outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.content ? publication.content.substring(0, 200) + \"...\" : \"Chapter 1: The Beginning\\nChapter 2: The Mystery Unfolds\\nChapter 3: Clues and Revelations\\nChapter 4: The Truth Emerges\\nChapter 5: Resolution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ps-30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-black space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Published:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"01 Feb 2024\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Genre:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: ((_publication_genre1 = publication.genre) === null || _publication_genre1 === void 0 ? void 0 : _publication_genre1.name) || \"Mystery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Format:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: publication.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Edition:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"1st\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Extent:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"ISBN:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"xxxxxxxxxxxxx\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Imprint:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Dimension:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320x153\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Publisher:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-black mt-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6\",\n                        children: \"About the Contributor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 h-96 bg-gray-200 border border-gray-300 mb-4 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Contributor Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-black font-semibold mb-4\",\n                                children: ((_publication_user1 = publication.user) === null || _publication_user1 === void 0 ? void 0 : _publication_user1.name) || \"Unknown Author\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-md text-black max-w-2xl leading-relaxed\",\n                                children: \"An accomplished author with years of experience in writing compelling narratives. Known for creating intricate plots and memorable characters that resonate with readers worldwide.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6 text-left\",\n                        children: \"Related Titles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-6 gap-4\",\n                        children: relatedBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full aspect-[159/256] bg-gray-200 border border-gray-300 mb-2 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"Book Cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-md text-black font-bold mb-1 line-clamp-2\",\n                                        children: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black mb-1\",\n                                        children: book.author\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black font-bold mb-1\",\n                                        children: book.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black\",\n                                        children: book.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, book.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicationDetailPage, \"itDfdi7VCw0YQBbk3nYPOKal+yo=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_3__.useAsync\n    ];\n});\n_c = PublicationDetailPage;\nvar _c;\n$RefreshReg$(_c, \"PublicationDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/repository/[id]/page.tsx\n"));

/***/ })

});