"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/repository/[id]/page",{

/***/ "(app-pages-browser)/./app/repository/[id]/page.tsx":
/*!**************************************!*\
  !*** ./app/repository/[id]/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicationDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineHeart!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaHeart!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_quantitycounter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/quantitycounter */ \"(app-pages-browser)/./app/components/quantitycounter.tsx\");\n/* harmony import */ var _components_AuthRequiredButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/AuthRequiredButton */ \"(app-pages-browser)/./app/components/AuthRequiredButton.tsx\");\n/* harmony import */ var _components_AuthRequiredLink__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/AuthRequiredLink */ \"(app-pages-browser)/./app/components/AuthRequiredLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Sample related books data\nconst relatedBooks = [\n    {\n        id: \"1\",\n        title: \"The Silent Patient\",\n        author: \"Alex Michaelides\",\n        price: \"3000frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"2\",\n        title: \"Gone Girl\",\n        author: \"Gillian Flynn\",\n        price: \"2500frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"3\",\n        title: \"The Girl with the Dragon Tattoo\",\n        author: \"Stieg Larsson\",\n        price: \"3500frs\",\n        type: \"eBook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"4\",\n        title: \"Big Little Lies\",\n        author: \"Liane Moriarty\",\n        price: \"2800frs\",\n        type: \"Audiobook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"5\",\n        title: \"The Woman in the Window\",\n        author: \"A.J. Finn\",\n        price: \"3200frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"6\",\n        title: \"Sharp Objects\",\n        author: \"Gillian Flynn\",\n        price: \"2700frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    }\n];\nfunction PublicationDetailPage(param) {\n    let { params } = param;\n    var _publication_genre, _publication_user, _publication_genre1, _publication_user1;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_3__.use)(params);\n    const [publication, setPublication] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"BOOK\"); // Default to printed copy\n    const [isInWishlist, setIsInWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [wishlistLoading, setWishlistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"PublicationDetailPage.useEffect\": ()=>{\n            async function fetchPublication() {\n                try {\n                    const response = await fetch(\"/api/repository/publication/\".concat(id));\n                    if (response.ok) {\n                        const data = await response.json();\n                        setPublication(data);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch publication:\", error);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchPublication();\n        }\n    }[\"PublicationDetailPage.useEffect\"], [\n        id\n    ]);\n    // Check wishlist status when publication or selectedType changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"PublicationDetailPage.useEffect\": ()=>{\n            async function checkWishlistStatus() {\n                if (!publication) return;\n                try {\n                    const response = await fetch(\"/api/wishlist/check?publicationId=\".concat(id, \"&selectedType=\").concat(selectedType));\n                    if (response.ok) {\n                        const data = await response.json();\n                        setIsInWishlist(data.isInWishlist);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to check wishlist status:\", error);\n                }\n            }\n            checkWishlistStatus();\n        }\n    }[\"PublicationDetailPage.useEffect\"], [\n        id,\n        selectedType,\n        publication\n    ]);\n    const handleWishlistToggle = async ()=>{\n        if (wishlistLoading) return;\n        setWishlistLoading(true);\n        try {\n            if (isInWishlist) {\n                // Remove from wishlist\n                const response = await fetch(\"/api/wishlist?publicationId=\".concat(id, \"&selectedType=\").concat(selectedType), {\n                    method: \"DELETE\"\n                });\n                if (response.ok) {\n                    setIsInWishlist(false);\n                }\n            } else {\n                // Add to wishlist\n                const response = await fetch(\"/api/wishlist\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        publicationId: id,\n                        selectedType\n                    })\n                });\n                if (response.ok) {\n                    setIsInWishlist(true);\n                }\n            }\n        } catch (error) {\n            console.error(\"Failed to update wishlist:\", error);\n        } finally{\n            setWishlistLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    if (!publication) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Publication not found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white mt-18\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-18 flex items-center justify-center\",\n                style: {\n                    backgroundColor: \"#f0eded\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/books\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/authors\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Authors\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/journals\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Journals\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-4 border-b border-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"text-s text-black\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"hover:underline\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        ((_publication_genre = publication.genre) === null || _publication_genre === void 0 ? void 0 : _publication_genre.parent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.parent.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.parent.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        publication.genre && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: publication.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2/5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-sm\",\n                                children: publication.cover ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: publication.cover,\n                                    alt: publication.title,\n                                    width: 270,\n                                    height: 380,\n                                    className: \"w-full h-auto border border-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full aspect-[270/380] bg-gray-200 border border-gray-300 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"No Cover Available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3/5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-black mb-4\",\n                                    children: publication.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-black mb-6\",\n                                    children: [\n                                        \"By \",\n                                        ((_publication_user = publication.user) === null || _publication_user === void 0 ? void 0 : _publication_user.name) || \"Unknown Author\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"border-blue-500 mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mt-20  gap-2.5 mb-35\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"BOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"BOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"Printed Copy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"3000frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"EBOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"EBOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"eBook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"2500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(\"AUDIOBOOK\"),\n                                            className: \"w-30 h-16 border flex flex-col items-center justify-center cursor-pointer transition-colors \".concat(selectedType === \"AUDIOBOOK\" ? \"border-blue-500 bg-gray-400\" : \"border-blue-300 bg-gray-300 hover:bg-gray-350\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800\",\n                                                    children: \"Audio book\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-800 font-bold\",\n                                                    children: \"3500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mt-20 mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl text-black\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quantitycounter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedType === \"EBOOK\" || selectedType === \"AUDIOBOOK\" || selectedType === \"JOURNAL\" || selectedType === \"ARTICLE\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: \"Available\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : // For physical books, you could add stock logic here\n                                        // For now, showing as available but this could be conditional based on actual stock\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: \"In stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthRequiredLink__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            href: \"/cart\",\n                                            className: \"px-8 py-4 w-70 h-11 text-white justify-center flex items-center text-xl hover:opacity-90 transition-opacity\",\n                                            style: {\n                                                backgroundColor: \"#0c0a46\"\n                                            },\n                                            children: \"Add to Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthRequiredButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            onClick: handleWishlistToggle,\n                                            disabled: wishlistLoading,\n                                            className: \"px-3 py-2 w-70 h-11 border border-black flex items-center justify-center gap-6 transition-colors \".concat(wishlistLoading ? \"bg-gray-200 cursor-not-allowed\" : \"bg-gray-300 hover:bg-gray-400\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl \".concat(isInWishlist ? \"text-red-500\" : \"text-gray-700\"),\n                                                    children: isInWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 35\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_8__.HiOutlineHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: isInWishlist ? \"Remove from wishlist\" : \"Add to wish list\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pe-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.abstract || \"A captivating mystery novel that will keep you on the edge of your seat. Follow the intricate plot as secrets unfold and characters navigate through unexpected twists and turns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Book Outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.content ? publication.content.substring(0, 200) + \"...\" : \"Chapter 1: The Beginning\\nChapter 2: The Mystery Unfolds\\nChapter 3: Clues and Revelations\\nChapter 4: The Truth Emerges\\nChapter 5: Resolution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ps-30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-black space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Published:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"01 Feb 2024\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Genre:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: ((_publication_genre1 = publication.genre) === null || _publication_genre1 === void 0 ? void 0 : _publication_genre1.name) || \"Mystery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Format:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: publication.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Edition:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"1st\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Extent:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"ISBN:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"xxxxxxxxxxxxx\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Imprint:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Dimension:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320x153\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Publisher:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-black mt-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6\",\n                        children: \"About the Contributor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 h-96 bg-gray-200 border border-gray-300 mb-4 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Contributor Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-black font-semibold mb-4\",\n                                children: ((_publication_user1 = publication.user) === null || _publication_user1 === void 0 ? void 0 : _publication_user1.name) || \"Unknown Author\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-md text-black max-w-2xl leading-relaxed\",\n                                children: \"An accomplished author with years of experience in writing compelling narratives. Known for creating intricate plots and memorable characters that resonate with readers worldwide.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6 text-left\",\n                        children: \"Related Titles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-6 gap-4\",\n                        children: relatedBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full aspect-[159/256] bg-gray-200 border border-gray-300 mb-2 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"Book Cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-md text-black font-bold mb-1 line-clamp-2\",\n                                        children: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black mb-1\",\n                                        children: book.author\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black font-bold mb-1\",\n                                        children: book.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black\",\n                                        children: book.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, book.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicationDetailPage, \"OJsV444y51xHPjoFV0DDQZjg9/c=\");\n_c = PublicationDetailPage;\nvar _c;\n$RefreshReg$(_c, \"PublicationDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/repository/[id]/page.tsx\n"));

/***/ })

});