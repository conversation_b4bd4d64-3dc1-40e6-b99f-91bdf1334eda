"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/publications/new/page",{

/***/ "(app-pages-browser)/./app/publications/new/page.tsx":
/*!***************************************!*\
  !*** ./app/publications/new/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewPublicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst publicationTypes = [\n    {\n        value: \"BOOK\",\n        label: \"Book\",\n        icon: \"📚\"\n    },\n    {\n        value: \"EBOOK\",\n        label: \"eBook\",\n        icon: \"💻\"\n    },\n    {\n        value: \"AUDIOBOOK\",\n        label: \"Audiobook\",\n        icon: \"🎧\"\n    },\n    {\n        value: \"JOURNAL\",\n        label: \"Journal\",\n        icon: \"📰\"\n    },\n    {\n        value: \"ARTICLE\",\n        label: \"Article\",\n        icon: \"📄\"\n    }\n];\nfunction NewPublicationPage() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [genres, setGenres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        abstract: \"\",\n        keywords: \"\",\n        type: \"BOOK\",\n        genreId: \"\",\n        cover: \"\"\n    });\n    const [publicationFile, setPublicationFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [fileError, setFileError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Fetch genres on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewPublicationPage.useEffect\": ()=>{\n            async function fetchGenres() {\n                try {\n                    const response = await fetch(\"/api/genres\");\n                    if (response.ok) {\n                        const data = await response.json();\n                        setGenres(data.genres || []);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch genres:\", error);\n                }\n            }\n            fetchGenres();\n        }\n    }[\"NewPublicationPage.useEffect\"], []);\n    // Show loading while session is loading\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    // Show sign-in prompt if not authenticated\n    if (!(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Sign in required\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-8\",\n                        children: \"You need to be signed in to add publications to your library.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/auth/signin?callbackUrl=/publications/new\",\n                        className: \"px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors\",\n                        children: \"Sign In\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        setFileError(\"\");\n        if (!file) {\n            setPublicationFile(null);\n            return;\n        }\n        // Validate file type\n        const allowedTypes = [\n            \"application/pdf\",\n            \"application/epub+zip\",\n            \"application/x-mobipocket-ebook\",\n            \"text/plain\",\n            \"application/msword\",\n            \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setFileError(\"Please upload a PDF, EPUB, MOBI, TXT, DOC, or DOCX file\");\n            setPublicationFile(null);\n            return;\n        }\n        // Validate file size (max 50MB)\n        const maxSize = 50 * 1024 * 1024; // 50MB\n        if (file.size > maxSize) {\n            setFileError(\"File size must be less than 50MB\");\n            setPublicationFile(null);\n            return;\n        }\n        setPublicationFile(file);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/publications\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    genreId: formData.genreId || null\n                })\n            });\n            if (response.ok) {\n                router.push(\"/dashboard/publications\");\n            } else {\n                const error = await response.json();\n                alert(error.message || \"Failed to create publication\");\n            }\n        } catch (error) {\n            console.error(\"Failed to create publication:\", error);\n            alert(\"Failed to create publication. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/dashboard/publications\",\n                                className: \"text-blue-900 hover:text-blue-700 font-medium\",\n                                children: \"← Back to Publications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Add New Publication\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Add an already published work to your library\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"title\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Title *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"title\",\n                                        name: \"title\",\n                                        required: true,\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700\",\n                                        placeholder: \"Enter the publication title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                        children: \"Publication Type *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-5 gap-3\",\n                                        children: publicationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-colors \".concat(formData.type === type.value ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        name: \"type\",\n                                                        value: type.value,\n                                                        checked: formData.type === type.value,\n                                                        onChange: handleInputChange,\n                                                        className: \"sr-only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: type.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: type.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, type.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"genreId\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Genre (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"genreId\",\n                                        name: \"genreId\",\n                                        value: formData.genreId,\n                                        onChange: handleInputChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select a genre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            genres.map((genre)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: genre.id,\n                                                    children: [\n                                                        genre.parent ? \"\".concat(genre.parent.name, \" > \") : \"\",\n                                                        genre.name\n                                                    ]\n                                                }, genre.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"abstract\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Abstract/Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"abstract\",\n                                        name: \"abstract\",\n                                        rows: 4,\n                                        value: formData.abstract,\n                                        onChange: handleInputChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700\",\n                                        placeholder: \"Brief description or abstract of the publication\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"keywords\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"keywords\",\n                                        name: \"keywords\",\n                                        value: formData.keywords,\n                                        onChange: handleInputChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700\",\n                                        placeholder: \"Enter keywords separated by commas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: \"Separate multiple keywords with commas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"cover\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Cover Image URL (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"cover\",\n                                        name: \"cover\",\n                                        value: formData.cover,\n                                        onChange: handleInputChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700\",\n                                        placeholder: \"https://example.com/cover-image.jpg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end gap-4 pt-6 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/dashboard/publications\",\n                                        className: \"px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !formData.title.trim(),\n                                        className: \"px-6 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        children: isSubmitting ? \"Adding...\" : \"Add Publication\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\publications\\\\new\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(NewPublicationPage, \"wiWabv3bKS5oj0e5HzUKgg9eSnI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewPublicationPage;\nvar _c;\n$RefreshReg$(_c, \"NewPublicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wdWJsaWNhdGlvbnMvbmV3L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDQTtBQUNDO0FBQ2hCO0FBRTdCLE1BQU1LLG1CQUFtQjtJQUN2QjtRQUFFQyxPQUFPO1FBQVFDLE9BQU87UUFBUUMsTUFBTTtJQUFLO0lBQzNDO1FBQUVGLE9BQU87UUFBU0MsT0FBTztRQUFTQyxNQUFNO0lBQUs7SUFDN0M7UUFBRUYsT0FBTztRQUFhQyxPQUFPO1FBQWFDLE1BQU07SUFBSztJQUNyRDtRQUFFRixPQUFPO1FBQVdDLE9BQU87UUFBV0MsTUFBTTtJQUFLO0lBQ2pEO1FBQUVGLE9BQU87UUFBV0MsT0FBTztRQUFXQyxNQUFNO0lBQUs7Q0FDbEQ7QUFFYyxTQUFTQztRQTBDakJDOztJQXpDTCxNQUFNLEVBQUVDLE1BQU1ELE9BQU8sRUFBRUUsTUFBTSxFQUFFLEdBQUdULDJEQUFVQTtJQUM1QyxNQUFNVSxTQUFTWCwwREFBU0E7SUFDeEIsTUFBTSxDQUFDWSxjQUFjQyxnQkFBZ0IsR0FBR2YsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZ0IsUUFBUUMsVUFBVSxHQUFHakIsK0NBQVFBLENBQUMsRUFBRTtJQUN2QyxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBQztRQUN2Q29CLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUczQiwrQ0FBUUEsQ0FBYztJQUNwRSxNQUFNLENBQUM0QixXQUFXQyxhQUFhLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUUzQyxrQ0FBa0M7SUFDbENDLGdEQUFTQTt3Q0FBQztZQUNSLGVBQWU2QjtnQkFDYixJQUFJO29CQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtvQkFDN0IsSUFBSUQsU0FBU0UsRUFBRSxFQUFFO3dCQUNmLE1BQU10QixPQUFPLE1BQU1vQixTQUFTRyxJQUFJO3dCQUNoQ2pCLFVBQVVOLEtBQUtLLE1BQU0sSUFBSSxFQUFFO29CQUM3QjtnQkFDRixFQUFFLE9BQU9tQixPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDM0M7WUFDRjtZQUNBTDtRQUNGO3VDQUFHLEVBQUU7SUFFTCx3Q0FBd0M7SUFDeEMsSUFBSWxCLFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQ3lCO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUFVOzs7Ozs7Ozs7OztJQUcvQjtJQUVBLDJDQUEyQztJQUMzQyxJQUFJLEVBQUM1QixvQkFBQUEsK0JBQUFBLGdCQUFBQSxRQUFTNkIsSUFBSSxjQUFiN0Isb0NBQUFBLGNBQWU4QixFQUFFLEdBQUU7UUFDdEIscUJBQ0UsOERBQUNIO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQWdCOzs7Ozs7a0NBQy9CLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FBd0M7Ozs7OztrQ0FHdEQsOERBQUNJO3dCQUFFSixXQUFVO2tDQUFxQjs7Ozs7O2tDQUdsQyw4REFBQ2xDLGtEQUFJQTt3QkFDSHVDLE1BQUs7d0JBQ0xMLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxNQUFNTSxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTSxFQUFFQyxJQUFJLEVBQUV4QyxLQUFLLEVBQUUsR0FBR3VDLEVBQUVFLE1BQU07UUFDaEM1QixZQUFZNkIsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNGLEtBQUssRUFBRXhDO1lBQU07SUFDaEQ7SUFFQSxNQUFNMkMsbUJBQW1CLENBQUNKO1lBQ1hBO1FBQWIsTUFBTUssUUFBT0wsa0JBQUFBLEVBQUVFLE1BQU0sQ0FBQ0ksS0FBSyxjQUFkTixzQ0FBQUEsZUFBZ0IsQ0FBQyxFQUFFO1FBQ2hDaEIsYUFBYTtRQUViLElBQUksQ0FBQ3FCLE1BQU07WUFDVHZCLG1CQUFtQjtZQUNuQjtRQUNGO1FBRUEscUJBQXFCO1FBQ3JCLE1BQU15QixlQUFlO1lBQ25CO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsSUFBSSxDQUFDQSxhQUFhQyxRQUFRLENBQUNILEtBQUszQixJQUFJLEdBQUc7WUFDckNNLGFBQWE7WUFDYkYsbUJBQW1CO1lBQ25CO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTTJCLFVBQVUsS0FBSyxPQUFPLE1BQU0sT0FBTztRQUN6QyxJQUFJSixLQUFLSyxJQUFJLEdBQUdELFNBQVM7WUFDdkJ6QixhQUFhO1lBQ2JGLG1CQUFtQjtZQUNuQjtRQUNGO1FBRUFBLG1CQUFtQnVCO0lBQ3JCO0lBRUEsTUFBTU0sZUFBZSxPQUFPWDtRQUMxQkEsRUFBRVksY0FBYztRQUNoQjFDLGdCQUFnQjtRQUVoQixJQUFJO1lBQ0YsTUFBTWdCLFdBQVcsTUFBTUMsTUFBTSxxQkFBcUI7Z0JBQ2hEMEIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CLEdBQUc1QyxRQUFRO29CQUNYTSxTQUFTTixTQUFTTSxPQUFPLElBQUk7Z0JBQy9CO1lBQ0Y7WUFFQSxJQUFJTyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2ZwQixPQUFPa0QsSUFBSSxDQUFDO1lBQ2QsT0FBTztnQkFDTCxNQUFNNUIsUUFBUSxNQUFNSixTQUFTRyxJQUFJO2dCQUNqQzhCLE1BQU03QixNQUFNOEIsT0FBTyxJQUFJO1lBQ3pCO1FBQ0YsRUFBRSxPQUFPOUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQzZCLE1BQU07UUFDUixTQUFVO1lBQ1JqRCxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDc0I7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNsQyxrREFBSUE7Z0NBQ0h1QyxNQUFLO2dDQUNMTCxXQUFVOzBDQUNYOzs7Ozs7Ozs7OztzQ0FJSCw4REFBQzRCOzRCQUFHNUIsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FHdEQsOERBQUNJOzRCQUFFSixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7OzhCQU0vQiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUM2Qjt3QkFBS0MsVUFBVVo7d0JBQWNsQixXQUFVOzswQ0FFdEMsOERBQUNEOztrREFDQyw4REFBQzlCO3dDQUFNOEQsU0FBUTt3Q0FBUS9CLFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hGLDhEQUFDZ0M7d0NBQ0MvQyxNQUFLO3dDQUNMaUIsSUFBRzt3Q0FDSE0sTUFBSzt3Q0FDTHlCLFFBQVE7d0NBQ1JqRSxPQUFPWSxTQUFTRSxLQUFLO3dDQUNyQm9ELFVBQVU1Qjt3Q0FDVk4sV0FBVTt3Q0FDVm1DLGFBQVk7Ozs7Ozs7Ozs7OzswQ0FLaEIsOERBQUNwQzs7a0RBQ0MsOERBQUM5Qjt3Q0FBTStCLFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hFLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmpDLGlCQUFpQnFFLEdBQUcsQ0FBQyxDQUFDbkQscUJBQ3JCLDhEQUFDaEI7Z0RBRUMrQixXQUFXLGdHQUlWLE9BSENwQixTQUFTSyxJQUFJLEtBQUtBLEtBQUtqQixLQUFLLEdBQ3hCLCtCQUNBOztrRUFHTiw4REFBQ2dFO3dEQUNDL0MsTUFBSzt3REFDTHVCLE1BQUs7d0RBQ0x4QyxPQUFPaUIsS0FBS2pCLEtBQUs7d0RBQ2pCcUUsU0FBU3pELFNBQVNLLElBQUksS0FBS0EsS0FBS2pCLEtBQUs7d0RBQ3JDa0UsVUFBVTVCO3dEQUNWTixXQUFVOzs7Ozs7a0VBRVosOERBQUNzQzt3REFBS3RDLFdBQVU7a0VBQWlCZixLQUFLZixJQUFJOzs7Ozs7a0VBQzFDLDhEQUFDb0U7d0RBQUt0QyxXQUFVO2tFQUNiZixLQUFLaEIsS0FBSzs7Ozs7OzsrQ0FqQlJnQixLQUFLakIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7OzswQ0F5QnZCLDhEQUFDK0I7O2tEQUNDLDhEQUFDOUI7d0NBQU04RCxTQUFRO3dDQUFVL0IsV0FBVTtrREFBK0M7Ozs7OztrREFHbEYsOERBQUN1Qzt3Q0FDQ3JDLElBQUc7d0NBQ0hNLE1BQUs7d0NBQ0x4QyxPQUFPWSxTQUFTTSxPQUFPO3dDQUN2QmdELFVBQVU1Qjt3Q0FDVk4sV0FBVTs7MERBRVYsOERBQUN3QztnREFBT3hFLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEJVLE9BQU8wRCxHQUFHLENBQUMsQ0FBQ0ssc0JBQ1gsOERBQUNEO29EQUFzQnhFLE9BQU95RSxNQUFNdkMsRUFBRTs7d0RBQ25DdUMsTUFBTUMsTUFBTSxHQUFHLEdBQXFCLE9BQWxCRCxNQUFNQyxNQUFNLENBQUNsQyxJQUFJLEVBQUMsU0FBTzt3REFBSWlDLE1BQU1qQyxJQUFJOzttREFEL0NpQyxNQUFNdkMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUTNCLDhEQUFDSDs7a0RBQ0MsOERBQUM5Qjt3Q0FBTThELFNBQVE7d0NBQVcvQixXQUFVO2tEQUErQzs7Ozs7O2tEQUduRiw4REFBQzJDO3dDQUNDekMsSUFBRzt3Q0FDSE0sTUFBSzt3Q0FDTG9DLE1BQU07d0NBQ041RSxPQUFPWSxTQUFTRyxRQUFRO3dDQUN4Qm1ELFVBQVU1Qjt3Q0FDVk4sV0FBVTt3Q0FDVm1DLGFBQVk7Ozs7Ozs7Ozs7OzswQ0FLaEIsOERBQUNwQzs7a0RBQ0MsOERBQUM5Qjt3Q0FBTThELFNBQVE7d0NBQVcvQixXQUFVO2tEQUErQzs7Ozs7O2tEQUduRiw4REFBQ2dDO3dDQUNDL0MsTUFBSzt3Q0FDTGlCLElBQUc7d0NBQ0hNLE1BQUs7d0NBQ0x4QyxPQUFPWSxTQUFTSSxRQUFRO3dDQUN4QmtELFVBQVU1Qjt3Q0FDVk4sV0FBVTt3Q0FDVm1DLGFBQVk7Ozs7OztrREFFZCw4REFBQy9CO3dDQUFFSixXQUFVO2tEQUE2Qjs7Ozs7Ozs7Ozs7OzBDQU01Qyw4REFBQ0Q7O2tEQUNDLDhEQUFDOUI7d0NBQU04RCxTQUFRO3dDQUFRL0IsV0FBVTtrREFBK0M7Ozs7OztrREFHaEYsOERBQUNnQzt3Q0FDQy9DLE1BQUs7d0NBQ0xpQixJQUFHO3dDQUNITSxNQUFLO3dDQUNMeEMsT0FBT1ksU0FBU08sS0FBSzt3Q0FDckIrQyxVQUFVNUI7d0NBQ1ZOLFdBQVU7d0NBQ1ZtQyxhQUFZOzs7Ozs7Ozs7Ozs7MENBS2hCLDhEQUFDcEM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDbEMsa0RBQUlBO3dDQUNIdUMsTUFBSzt3Q0FDTEwsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDNkM7d0NBQ0M1RCxNQUFLO3dDQUNMNkQsVUFBVXRFLGdCQUFnQixDQUFDSSxTQUFTRSxLQUFLLENBQUNpRSxJQUFJO3dDQUM5Qy9DLFdBQVU7a0RBRVR4QixlQUFlLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFROUM7R0E3U3dCTDs7UUFDWU4sdURBQVVBO1FBQzdCRCxzREFBU0E7OztLQUZGTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcVGluYSBFZHVjYXRpb24ub3JnXFxSZXZpZXcgUmVxdWVzdCBiYWNrZW5kXFxBUlJTLU5leHRKU1xcdGluYS1lZHVjYXRpb25cXGFwcFxccHVibGljYXRpb25zXFxuZXdcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuXG5jb25zdCBwdWJsaWNhdGlvblR5cGVzID0gW1xuICB7IHZhbHVlOiBcIkJPT0tcIiwgbGFiZWw6IFwiQm9va1wiLCBpY29uOiBcIvCfk5pcIiB9LFxuICB7IHZhbHVlOiBcIkVCT09LXCIsIGxhYmVsOiBcImVCb29rXCIsIGljb246IFwi8J+Su1wiIH0sXG4gIHsgdmFsdWU6IFwiQVVESU9CT09LXCIsIGxhYmVsOiBcIkF1ZGlvYm9va1wiLCBpY29uOiBcIvCfjqdcIiB9LFxuICB7IHZhbHVlOiBcIkpPVVJOQUxcIiwgbGFiZWw6IFwiSm91cm5hbFwiLCBpY29uOiBcIvCfk7BcIiB9LFxuICB7IHZhbHVlOiBcIkFSVElDTEVcIiwgbGFiZWw6IFwiQXJ0aWNsZVwiLCBpY29uOiBcIvCfk4RcIiB9LFxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmV3UHVibGljYXRpb25QYWdlKCkge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW2lzU3VibWl0dGluZywgc2V0SXNTdWJtaXR0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2dlbnJlcywgc2V0R2VucmVzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgdGl0bGU6IFwiXCIsXG4gICAgYWJzdHJhY3Q6IFwiXCIsXG4gICAga2V5d29yZHM6IFwiXCIsXG4gICAgdHlwZTogXCJCT09LXCIsXG4gICAgZ2VucmVJZDogXCJcIixcbiAgICBjb3ZlcjogXCJcIixcbiAgfSk7XG4gIGNvbnN0IFtwdWJsaWNhdGlvbkZpbGUsIHNldFB1YmxpY2F0aW9uRmlsZV0gPSB1c2VTdGF0ZTxGaWxlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtmaWxlRXJyb3IsIHNldEZpbGVFcnJvcl0gPSB1c2VTdGF0ZShcIlwiKTtcblxuICAvLyBGZXRjaCBnZW5yZXMgb24gY29tcG9uZW50IG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgYXN5bmMgZnVuY3Rpb24gZmV0Y2hHZW5yZXMoKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFwiL2FwaS9nZW5yZXNcIik7XG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgc2V0R2VucmVzKGRhdGEuZ2VucmVzIHx8IFtdKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBnZW5yZXM6XCIsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gICAgZmV0Y2hHZW5yZXMoKTtcbiAgfSwgW10pO1xuXG4gIC8vIFNob3cgbG9hZGluZyB3aGlsZSBzZXNzaW9uIGlzIGxvYWRpbmdcbiAgaWYgKHN0YXR1cyA9PT0gXCJsb2FkaW5nXCIpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteGxcIj5Mb2FkaW5nLi4uPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gU2hvdyBzaWduLWluIHByb21wdCBpZiBub3QgYXV0aGVudGljYXRlZFxuICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPvCflJI8L2Rpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgU2lnbiBpbiByZXF1aXJlZFxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi04XCI+XG4gICAgICAgICAgICBZb3UgbmVlZCB0byBiZSBzaWduZWQgaW4gdG8gYWRkIHB1YmxpY2F0aW9ucyB0byB5b3VyIGxpYnJhcnkuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICBocmVmPVwiL2F1dGgvc2lnbmluP2NhbGxiYWNrVXJsPS9wdWJsaWNhdGlvbnMvbmV3XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1ibHVlLTkwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS04MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFNpZ24gSW5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MVGV4dEFyZWFFbGVtZW50IHwgSFRNTFNlbGVjdEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSB9ID0gZS50YXJnZXQ7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbbmFtZV06IHZhbHVlIH0pKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVGaWxlQ2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgc2V0RmlsZUVycm9yKFwiXCIpO1xuXG4gICAgaWYgKCFmaWxlKSB7XG4gICAgICBzZXRQdWJsaWNhdGlvbkZpbGUobnVsbCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSB0eXBlXG4gICAgY29uc3QgYWxsb3dlZFR5cGVzID0gW1xuICAgICAgXCJhcHBsaWNhdGlvbi9wZGZcIixcbiAgICAgIFwiYXBwbGljYXRpb24vZXB1Yit6aXBcIixcbiAgICAgIFwiYXBwbGljYXRpb24veC1tb2JpcG9ja2V0LWVib29rXCIsXG4gICAgICBcInRleHQvcGxhaW5cIixcbiAgICAgIFwiYXBwbGljYXRpb24vbXN3b3JkXCIsXG4gICAgICBcImFwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50XCIsXG4gICAgXTtcblxuICAgIGlmICghYWxsb3dlZFR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcbiAgICAgIHNldEZpbGVFcnJvcihcIlBsZWFzZSB1cGxvYWQgYSBQREYsIEVQVUIsIE1PQkksIFRYVCwgRE9DLCBvciBET0NYIGZpbGVcIik7XG4gICAgICBzZXRQdWJsaWNhdGlvbkZpbGUobnVsbCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSBzaXplIChtYXggNTBNQilcbiAgICBjb25zdCBtYXhTaXplID0gNTAgKiAxMDI0ICogMTAyNDsgLy8gNTBNQlxuICAgIGlmIChmaWxlLnNpemUgPiBtYXhTaXplKSB7XG4gICAgICBzZXRGaWxlRXJyb3IoXCJGaWxlIHNpemUgbXVzdCBiZSBsZXNzIHRoYW4gNTBNQlwiKTtcbiAgICAgIHNldFB1YmxpY2F0aW9uRmlsZShudWxsKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRQdWJsaWNhdGlvbkZpbGUoZmlsZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvcHVibGljYXRpb25zXCIsIHtcbiAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgLi4uZm9ybURhdGEsXG4gICAgICAgICAgZ2VucmVJZDogZm9ybURhdGEuZ2VucmVJZCB8fCBudWxsLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgcm91dGVyLnB1c2goXCIvZGFzaGJvYXJkL3B1YmxpY2F0aW9uc1wiKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBhbGVydChlcnJvci5tZXNzYWdlIHx8IFwiRmFpbGVkIHRvIGNyZWF0ZSBwdWJsaWNhdGlvblwiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBjcmVhdGUgcHVibGljYXRpb246XCIsIGVycm9yKTtcbiAgICAgIGFsZXJ0KFwiRmFpbGVkIHRvIGNyZWF0ZSBwdWJsaWNhdGlvbi4gUGxlYXNlIHRyeSBhZ2Fpbi5cIik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBweS04XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgbWItNFwiPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvcHVibGljYXRpb25zXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTkwMCBob3Zlcjp0ZXh0LWJsdWUtNzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4oaQIEJhY2sgdG8gUHVibGljYXRpb25zXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgIEFkZCBOZXcgUHVibGljYXRpb25cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIEFkZCBhbiBhbHJlYWR5IHB1Ymxpc2hlZCB3b3JrIHRvIHlvdXIgbGlicmFyeVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvcm0gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLThcIj5cbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHsvKiBUaXRsZSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidGl0bGVcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIFRpdGxlICpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIGlkPVwidGl0bGVcIlxuICAgICAgICAgICAgICAgIG5hbWU9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGl0bGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB0aGUgcHVibGljYXRpb24gdGl0bGVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBQdWJsaWNhdGlvbiBUeXBlICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgUHVibGljYXRpb24gVHlwZSAqXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNSBnYXAtM1wiPlxuICAgICAgICAgICAgICAgIHtwdWJsaWNhdGlvblR5cGVzLm1hcCgodHlwZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICAgIGtleT17dHlwZS52YWx1ZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcC00IGJvcmRlci0yIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS50eXBlID09PSB0eXBlLnZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwidHlwZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3R5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEudHlwZSA9PT0gdHlwZS52YWx1ZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTJcIj57dHlwZS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3R5cGUubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBHZW5yZSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZ2VucmVJZFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgR2VucmUgKE9wdGlvbmFsKVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgaWQ9XCJnZW5yZUlkXCJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZ2VucmVJZFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmdlbnJlSWR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+U2VsZWN0IGEgZ2VucmU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICB7Z2VucmVzLm1hcCgoZ2VucmU6IGFueSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2dlbnJlLmlkfSB2YWx1ZT17Z2VucmUuaWR9PlxuICAgICAgICAgICAgICAgICAgICB7Z2VucmUucGFyZW50ID8gYCR7Z2VucmUucGFyZW50Lm5hbWV9ID4gYCA6IFwiXCJ9e2dlbnJlLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFic3RyYWN0ICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJhYnN0cmFjdFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgQWJzdHJhY3QvRGVzY3JpcHRpb25cbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgaWQ9XCJhYnN0cmFjdFwiXG4gICAgICAgICAgICAgICAgbmFtZT1cImFic3RyYWN0XCJcbiAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hYnN0cmFjdH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJyaWVmIGRlc2NyaXB0aW9uIG9yIGFic3RyYWN0IG9mIHRoZSBwdWJsaWNhdGlvblwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEtleXdvcmRzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJrZXl3b3Jkc1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgS2V5d29yZHNcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIGlkPVwia2V5d29yZHNcIlxuICAgICAgICAgICAgICAgIG5hbWU9XCJrZXl3b3Jkc1wiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmtleXdvcmRzfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS03MDBcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIga2V5d29yZHMgc2VwYXJhdGVkIGJ5IGNvbW1hc1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgU2VwYXJhdGUgbXVsdGlwbGUga2V5d29yZHMgd2l0aCBjb21tYXNcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb3ZlciBJbWFnZSBVUkwgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImNvdmVyXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBDb3ZlciBJbWFnZSBVUkwgKE9wdGlvbmFsKVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidXJsXCJcbiAgICAgICAgICAgICAgICBpZD1cImNvdmVyXCJcbiAgICAgICAgICAgICAgICBuYW1lPVwiY292ZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb3Zlcn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vY292ZXItaW1hZ2UuanBnXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogU3VibWl0IEJ1dHRvbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kIGdhcC00IHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvcHVibGljYXRpb25zXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTIgdGV4dC1ncmF5LTcwMCBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nIHx8ICFmb3JtRGF0YS50aXRsZS50cmltKCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0yIGJnLWJsdWUtOTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTgwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gXCJBZGRpbmcuLi5cIiA6IFwiQWRkIFB1YmxpY2F0aW9uXCJ9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9mb3JtPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlU2Vzc2lvbiIsIkxpbmsiLCJwdWJsaWNhdGlvblR5cGVzIiwidmFsdWUiLCJsYWJlbCIsImljb24iLCJOZXdQdWJsaWNhdGlvblBhZ2UiLCJzZXNzaW9uIiwiZGF0YSIsInN0YXR1cyIsInJvdXRlciIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImdlbnJlcyIsInNldEdlbnJlcyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJ0aXRsZSIsImFic3RyYWN0Iiwia2V5d29yZHMiLCJ0eXBlIiwiZ2VucmVJZCIsImNvdmVyIiwicHVibGljYXRpb25GaWxlIiwic2V0UHVibGljYXRpb25GaWxlIiwiZmlsZUVycm9yIiwic2V0RmlsZUVycm9yIiwiZmV0Y2hHZW5yZXMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwidXNlciIsImlkIiwiaDIiLCJwIiwiaHJlZiIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZSIsIm5hbWUiLCJ0YXJnZXQiLCJwcmV2IiwiaGFuZGxlRmlsZUNoYW5nZSIsImZpbGUiLCJmaWxlcyIsImFsbG93ZWRUeXBlcyIsImluY2x1ZGVzIiwibWF4U2l6ZSIsInNpemUiLCJoYW5kbGVTdWJtaXQiLCJwcmV2ZW50RGVmYXVsdCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInB1c2giLCJhbGVydCIsIm1lc3NhZ2UiLCJoMSIsImZvcm0iLCJvblN1Ym1pdCIsImh0bWxGb3IiLCJpbnB1dCIsInJlcXVpcmVkIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsIm1hcCIsImNoZWNrZWQiLCJzcGFuIiwic2VsZWN0Iiwib3B0aW9uIiwiZ2VucmUiLCJwYXJlbnQiLCJ0ZXh0YXJlYSIsInJvd3MiLCJidXR0b24iLCJkaXNhYmxlZCIsInRyaW0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/publications/new/page.tsx\n"));

/***/ })

});