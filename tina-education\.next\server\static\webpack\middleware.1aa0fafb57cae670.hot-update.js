"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(middleware)/./auth.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)((req)=>{\n    const { pathname } = req.nextUrl;\n    // Check authentication - req.auth contains the session if user is authenticated\n    // In NextAuth v5, req.auth should contain the session object\n    const session = req.auth;\n    const isAuthenticated = !!(session?.user?.id || session?.user?.email);\n    // Protected routes that require authentication\n    const protectedRoutes = [\n        \"/dashboard\",\n        \"/manuscripts\",\n        \"/reviews\"\n    ];\n    // Check if the current path is a protected route\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // Debug logging (remove in production)\n    console.log(`🔐 Middleware: ${pathname}`);\n    console.log(`🔐 Session exists:`, !!session);\n    console.log(`🔐 Session user:`, session?.user);\n    console.log(`🔐 Is authenticated:`, isAuthenticated);\n    console.log(`🔐 Is protected route:`, isProtectedRoute);\n    // If it's a protected route and user is not authenticated, redirect to sign in\n    if (isProtectedRoute && !isAuthenticated) {\n        console.log(`🚫 Redirecting unauthenticated user from ${pathname} to sign in`);\n        const signInUrl = new URL(\"/auth/signin\", req.url);\n        signInUrl.searchParams.set(\"callbackUrl\", pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(signInUrl);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!api|_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});