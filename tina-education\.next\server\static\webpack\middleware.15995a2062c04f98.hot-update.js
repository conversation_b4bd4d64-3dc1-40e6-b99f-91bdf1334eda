"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(middleware)/./auth.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)((req)=>{\n    const { pathname } = req.nextUrl;\n    const isAuthenticated = !!req.auth?.user;\n    // Protected routes that require authentication\n    const protectedRoutes = [\n        \"/dashboard\",\n        \"/manuscripts\",\n        \"/reviews\"\n    ];\n    // Check if the current path is a protected route\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // If it's a protected route and user is not authenticated, redirect to sign in\n    if (isProtectedRoute && !isAuthenticated) {\n        const signInUrl = new URL(\"/auth/signin\", req.url);\n        signInUrl.searchParams.set(\"callbackUrl\", pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(signInUrl);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!api|_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});