{"/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/wishlist/check/route": "app/api/wishlist/check/route.js", "/api/wishlist/route": "app/api/wishlist/route.js", "/api/repository/publication/[id]/route": "app/api/repository/publication/[id]/route.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/page": "app/page.js", "/auth/signin/page": "app/auth/signin/page.js"}