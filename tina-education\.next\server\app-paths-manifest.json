{"/api/wishlist/route": "app/api/wishlist/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/repository/publication/[id]/route": "app/api/repository/publication/[id]/route.js", "/api/wishlist/check/route": "app/api/wishlist/check/route.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/dashboard/page": "app/dashboard/page.js"}