{"/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/library/page": "app/dashboard/library/page.js", "/dashboard/publications/page": "app/dashboard/publications/page.js"}