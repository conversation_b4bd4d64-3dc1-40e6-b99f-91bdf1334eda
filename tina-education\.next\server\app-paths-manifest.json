{"/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/wishlist/route": "app/api/wishlist/route.js", "/api/repository/books/route": "app/api/repository/books/route.js", "/api/repository/publication/[id]/route": "app/api/repository/publication/[id]/route.js", "/api/wishlist/check/route": "app/api/wishlist/check/route.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/page": "app/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/wishlist/page": "app/wishlist/page.js", "/cart/page": "app/cart/page.js", "/books/page": "app/books/page.js"}