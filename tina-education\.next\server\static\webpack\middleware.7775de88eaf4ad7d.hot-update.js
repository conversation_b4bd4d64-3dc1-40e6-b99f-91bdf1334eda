"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(middleware)/./auth.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)((req)=>{\n    const { pathname } = req.nextUrl;\n    // Check authentication - req.auth contains the session if user is authenticated\n    const isAuthenticated = !!req.auth;\n    // Protected routes that require authentication\n    const protectedRoutes = [\n        \"/dashboard\",\n        \"/manuscripts\",\n        \"/reviews\"\n    ];\n    // Check if the current path is a protected route\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // Debug logging (remove in production)\n    if (isProtectedRoute) {\n        console.log(`🔐 Middleware: ${pathname}, Auth: ${isAuthenticated}, Session:`, req.auth);\n    }\n    // If it's a protected route and user is not authenticated, redirect to sign in\n    if (isProtectedRoute && !isAuthenticated) {\n        console.log(`🚫 Redirecting unauthenticated user from ${pathname} to sign in`);\n        const signInUrl = new URL(\"/auth/signin\", req.url);\n        signInUrl.searchParams.set(\"callbackUrl\", pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(signInUrl);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!api|_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});