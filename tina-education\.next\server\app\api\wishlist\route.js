/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wishlist/route";
exports.ids = ["app/api/wishlist/route"];
exports.modules = {

/***/ "(rsc)/./app/api/wishlist/route.ts":
/*!***********************************!*\
  !*** ./app/api/wishlist/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n\n\n\n// GET - Get user's wishlist\nasync function GET(request) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const wishlistItems = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.wishlistItem.findMany({\n            where: {\n                userId: session.user.id\n            },\n            include: {\n                publication: {\n                    include: {\n                        user: {\n                            select: {\n                                name: true,\n                                email: true\n                            }\n                        },\n                        genre: {\n                            include: {\n                                parent: true\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            wishlistItems: wishlistItems.map((item)=>({\n                    ...item,\n                    createdAt: item.createdAt.toISOString(),\n                    updatedAt: item.updatedAt.toISOString(),\n                    publication: {\n                        ...item.publication,\n                        createdAt: item.publication.createdAt.toISOString(),\n                        updatedAt: item.publication.updatedAt.toISOString()\n                    }\n                }))\n        });\n    } catch (error) {\n        console.error(\"Failed to fetch wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch wishlist\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Add item to wishlist\nasync function POST(request) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const { publicationId, selectedType } = await request.json();\n        if (!publicationId || !selectedType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Publication ID and selected type are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if publication exists\n        const publication = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.findUnique({\n            where: {\n                id: publicationId\n            }\n        });\n        if (!publication) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Publication not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if item already exists in wishlist\n        const existingItem = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.wishlistItem.findUnique({\n            where: {\n                userId_publicationId_selectedType: {\n                    userId: session.user.id,\n                    publicationId,\n                    selectedType: selectedType\n                }\n            }\n        });\n        if (existingItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Item already in wishlist\"\n            }, {\n                status: 409\n            });\n        }\n        // Add to wishlist\n        const wishlistItem = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.wishlistItem.create({\n            data: {\n                userId: session.user.id,\n                publicationId,\n                selectedType: selectedType\n            },\n            include: {\n                publication: {\n                    include: {\n                        user: {\n                            select: {\n                                name: true,\n                                email: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...wishlistItem,\n            createdAt: wishlistItem.createdAt.toISOString(),\n            updatedAt: wishlistItem.updatedAt.toISOString(),\n            publication: {\n                ...wishlistItem.publication,\n                createdAt: wishlistItem.publication.createdAt.toISOString(),\n                updatedAt: wishlistItem.publication.updatedAt.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to add to wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add to wishlist\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Remove item from wishlist\nasync function DELETE(request) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const { searchParams } = new URL(request.url);\n        const publicationId = searchParams.get(\"publicationId\");\n        const selectedType = searchParams.get(\"selectedType\");\n        if (!publicationId || !selectedType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Publication ID and selected type are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Remove from wishlist\n        const deletedItem = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.wishlistItem.delete({\n            where: {\n                userId_publicationId_selectedType: {\n                    userId: session.user.id,\n                    publicationId,\n                    selectedType: selectedType\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Item removed from wishlist\",\n            deletedItem: {\n                ...deletedItem,\n                createdAt: deletedItem.createdAt.toISOString(),\n                updatedAt: deletedItem.updatedAt.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to remove from wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to remove from wishlist\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/wishlist/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_wishlist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/wishlist/route.ts */ \"(rsc)/./app/api/wishlist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wishlist/route\",\n        pathname: \"/api/wishlist\",\n        filename: \"route\",\n        bundlePath: \"app/api/wishlist/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\wishlist\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_wishlist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();