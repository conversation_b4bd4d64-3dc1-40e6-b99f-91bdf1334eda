/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/publications/route";
exports.ids = ["app/api/publications/route"];
exports.modules = {

/***/ "(rsc)/./app/api/publications/route.ts":
/*!***************************************!*\
  !*** ./app/api/publications/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var _vercel_blob__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vercel/blob */ \"(rsc)/./node_modules/@vercel/blob/dist/index.js\");\n\n\n\n\n// GET - Get user's publications\nasync function GET() {\n    try {\n        const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const publications = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.findMany({\n            where: {\n                author_id: session.user.id\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                genre: {\n                    select: {\n                        id: true,\n                        name: true,\n                        slug: true,\n                        parent: {\n                            select: {\n                                id: true,\n                                name: true,\n                                slug: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            publications\n        });\n    } catch (error) {\n        console.error(\"Failed to fetch publications:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch publications\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new publication\nasync function POST(request) {\n    try {\n        const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Handle FormData for file uploads\n        const formData = await request.formData();\n        const title = formData.get(\"title\");\n        const abstract = formData.get(\"abstract\");\n        const keywords = formData.get(\"keywords\");\n        const type = formData.get(\"type\");\n        const genreId = formData.get(\"genreId\");\n        const cover = formData.get(\"cover\");\n        const publicationFile = formData.get(\"publicationFile\");\n        // Validate required fields\n        if (!title?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate publication type\n        const validTypes = [\n            \"JOURNAL\",\n            \"ARTICLE\",\n            \"BOOK\",\n            \"EBOOK\",\n            \"AUDIOBOOK\"\n        ];\n        if (!validTypes.includes(type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid publication type\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate genre if provided\n        if (genreId && genreId.trim()) {\n            const genreExists = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.genre.findUnique({\n                where: {\n                    id: genreId\n                }\n            });\n            if (!genreExists) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid genre selected\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Handle file upload if provided\n        let fileUrl = null;\n        let fileName = null;\n        if (publicationFile && publicationFile.size > 0) {\n            // Validate file type\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/epub+zip\",\n                \"application/x-mobipocket-ebook\",\n                \"text/plain\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n            ];\n            if (!allowedTypes.includes(publicationFile.type)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid file type. Please upload PDF, EPUB, MOBI, TXT, DOC, or DOCX files only.\"\n                }, {\n                    status: 400\n                });\n            }\n            // Validate file size (50MB limit)\n            const maxSize = 50 * 1024 * 1024; // 50MB\n            if (publicationFile.size > maxSize) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"File size too large. Maximum size is 50MB.\"\n                }, {\n                    status: 400\n                });\n            }\n            // Generate unique filename\n            const timestamp = Date.now();\n            const randomString = Math.random().toString(36).substring(7);\n            const fileExtension = publicationFile.name.split(\".\").pop();\n            const uniqueFilename = `publication-${timestamp}-${randomString}.${fileExtension}`;\n            try {\n                // Upload to Vercel Blob\n                const blob = await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_3__.put)(`publications/${uniqueFilename}`, publicationFile, {\n                    access: \"public\",\n                    contentType: publicationFile.type\n                });\n                fileUrl = blob.url;\n                fileName = publicationFile.name;\n                console.log(`✅ Publication file uploaded successfully: ${blob.url}`);\n            } catch (uploadError) {\n                console.error(\"❌ File upload failed:\", uploadError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to upload file\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // Create the publication\n        const publication = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.create({\n            data: {\n                title: title.trim(),\n                abstract: abstract?.trim() || null,\n                keywords: keywords?.trim() || null,\n                type: type,\n                genreId: genreId && genreId.trim() || null,\n                cover: cover?.trim() || null,\n                fileUrl: fileUrl,\n                fileName: fileName,\n                author_id: session.user.id\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                genre: {\n                    select: {\n                        id: true,\n                        name: true,\n                        slug: true,\n                        parent: {\n                            select: {\n                                id: true,\n                                name: true,\n                                slug: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Publication created successfully\",\n            publication\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Failed to create publication:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create publication\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/publications/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_publications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/publications/route.ts */ \"(rsc)/./app/api/publications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/publications/route\",\n        pathname: \"/api/publications\",\n        filename: \"route\",\n        bundlePath: \"app/api/publications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\publications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_publications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/undici","vendor-chunks/@fastify","vendor-chunks/retry","vendor-chunks/@vercel","vendor-chunks/is-node-process","vendor-chunks/throttleit","vendor-chunks/is-buffer","vendor-chunks/async-retry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();