"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(middleware)/./auth.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)((req)=>{\n    // The authorized callback in auth.ts handles the authentication logic\n    // This middleware just needs to pass through the request\n    console.log(`🔐 Middleware: ${req.nextUrl.pathname}, Auth:`, !!req.auth);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!api|_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbWlkZGxld2FyZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZCO0FBQ2E7QUFFMUMsaUVBQWVBLDJDQUFJQSxDQUFDLENBQUNFO0lBQ25CLHNFQUFzRTtJQUN0RSx5REFBeUQ7SUFDekRDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGVBQWUsRUFBRUYsSUFBSUcsT0FBTyxDQUFDQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUFDSixJQUFJRixJQUFJO0lBQ3ZFLE9BQU9DLHFEQUFZQSxDQUFDTSxJQUFJO0FBQzFCLEVBQUU7QUFFSyxNQUFNQyxTQUFTO0lBQ3BCQyxTQUFTO1FBQ1A7Ozs7OztLQU1DLEdBQ0Q7S0FDRDtBQUNILEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxtaWRkbGV3YXJlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGF1dGggfSBmcm9tIFwiQC9hdXRoXCJcbmltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiXG5cbmV4cG9ydCBkZWZhdWx0IGF1dGgoKHJlcSkgPT4ge1xuICAvLyBUaGUgYXV0aG9yaXplZCBjYWxsYmFjayBpbiBhdXRoLnRzIGhhbmRsZXMgdGhlIGF1dGhlbnRpY2F0aW9uIGxvZ2ljXG4gIC8vIFRoaXMgbWlkZGxld2FyZSBqdXN0IG5lZWRzIHRvIHBhc3MgdGhyb3VnaCB0aGUgcmVxdWVzdFxuICBjb25zb2xlLmxvZyhg8J+UkCBNaWRkbGV3YXJlOiAke3JlcS5uZXh0VXJsLnBhdGhuYW1lfSwgQXV0aDpgLCAhIXJlcS5hdXRoKTtcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5uZXh0KCk7XG59KVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBtYXRjaGVyOiBbXG4gICAgLypcbiAgICAgKiBNYXRjaCBhbGwgcmVxdWVzdCBwYXRocyBleGNlcHQgZm9yIHRoZSBvbmVzIHN0YXJ0aW5nIHdpdGg6XG4gICAgICogLSBhcGkgKEFQSSByb3V0ZXMpXG4gICAgICogLSBfbmV4dC9zdGF0aWMgKHN0YXRpYyBmaWxlcylcbiAgICAgKiAtIF9uZXh0L2ltYWdlIChpbWFnZSBvcHRpbWl6YXRpb24gZmlsZXMpXG4gICAgICogLSBmYXZpY29uLmljbyAoZmF2aWNvbiBmaWxlKVxuICAgICAqL1xuICAgIFwiLygoPyFhcGl8X25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvKS4qKVwiLFxuICBdLFxufVxuIl0sIm5hbWVzIjpbImF1dGgiLCJOZXh0UmVzcG9uc2UiLCJyZXEiLCJjb25zb2xlIiwibG9nIiwibmV4dFVybCIsInBhdGhuYW1lIiwibmV4dCIsImNvbmZpZyIsIm1hdGNoZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});