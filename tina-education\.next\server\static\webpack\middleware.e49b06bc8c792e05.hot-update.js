"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(middleware)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(middleware)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(middleware)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(middleware)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(middleware)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./auth.ts\n");

/***/ })

});