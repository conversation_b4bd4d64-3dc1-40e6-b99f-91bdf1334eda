"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(middleware)/./auth.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)((req)=>{\n    const { pathname } = req.nextUrl;\n    // Check authentication - req.auth contains the session if user is authenticated\n    // In NextAuth v5, req.auth should contain the session object\n    const session = req.auth;\n    const isAuthenticated = !!(session?.user?.id || session?.user?.email);\n    // Protected routes that require authentication\n    const protectedRoutes = [\n        \"/dashboard\",\n        \"/manuscripts\",\n        \"/reviews\"\n    ];\n    // Check if the current path is a protected route\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // Debug logging (remove in production)\n    if (isProtectedRoute) {\n        console.log(`🔐 Middleware: ${pathname}, Auth: ${isAuthenticated}, Session:`, session);\n    }\n    // If it's a protected route and user is not authenticated, redirect to sign in\n    if (isProtectedRoute && !isAuthenticated) {\n        console.log(`🚫 Redirecting unauthenticated user from ${pathname} to sign in`);\n        const signInUrl = new URL(\"/auth/signin\", req.url);\n        signInUrl.searchParams.set(\"callbackUrl\", pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(signInUrl);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ \"/((?!api|_next/static|_next/image|favicon.ico).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});