/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/notifications/route";
exports.ids = ["app/api/auth/notifications/route"];
exports.modules = {

/***/ "(rsc)/./app/api/auth/notifications/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/notifications/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\nasync function GET() {\n    try {\n        const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)();\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const [notifications, unreadCount] = await Promise.all([\n            _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.findMany({\n                where: {\n                    userId: session.user.id\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 10\n            }),\n            _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.count({\n                where: {\n                    userId: session.user.id,\n                    isRead: false\n                }\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            notifications: notifications.map((notification)=>({\n                    ...notification,\n                    createdAt: notification.createdAt.toISOString()\n                })),\n            unreadCount\n        });\n    } catch (error) {\n        console.error(\"Failed to fetch notifications:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Failed to fetch notifications\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/notifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fnotifications%2Froute&page=%2Fapi%2Fauth%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fnotifications%2Froute&page=%2Fapi%2Fauth%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_auth_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/notifications/route.ts */ \"(rsc)/./app/api/auth/notifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/notifications/route\",\n        pathname: \"/api/auth/notifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/notifications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\auth\\\\notifications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_auth_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fnotifications%2Froute&page=%2Fapi%2Fauth%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fnotifications%2Froute&page=%2Fapi%2Fauth%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();